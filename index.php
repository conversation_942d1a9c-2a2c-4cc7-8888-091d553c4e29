<?php
/**
 * Smart Communication System - Main Entry Point
 * Jordan-based Communication System for Companies, Schools, Government & Private Sector
 */

require_once 'config/config.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('auth/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'] ?? 'المستخدم';

// Get dashboard statistics
try {
    // Total clients
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM clients WHERE assigned_employee_id = ? OR ? = 'admin'");
    $stmt->execute([$user_id, $user_role]);
    $total_clients = $stmt->fetch()['total'];
    
    // Total communications today
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total 
        FROM communications c 
        JOIN clients cl ON c.client_id = cl.id 
        WHERE DATE(c.sent_at) = CURDATE() 
        AND (c.employee_id = ? OR ? = 'admin' OR (? = 'supervisor' AND cl.assigned_employee_id IN (SELECT id FROM users WHERE role = 'employee')))
    ");
    $stmt->execute([$user_id, $user_role, $user_role]);
    $today_communications = $stmt->fetch()['total'];
    
    // Pending follow-ups
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total 
        FROM clients c 
        LEFT JOIN communications com ON c.id = com.client_id 
        WHERE (c.assigned_employee_id = ? OR ? = 'admin') 
        AND (com.sent_at IS NULL OR com.sent_at < DATE_SUB(NOW(), INTERVAL 7 DAY))
    ");
    $stmt->execute([$user_id, $user_role]);
    $pending_followups = $stmt->fetch()['total'];
    
    // Active employees (for admin/supervisor)
    $active_employees = 0;
    if ($user_role == 'admin' || $user_role == 'supervisor') {
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM users WHERE is_active = 1 AND role != 'admin'");
        $stmt->execute();
        $active_employees = $stmt->fetch()['total'];
    }
    
} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    $total_clients = $today_communications = $pending_followups = $active_employees = 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-chat-dots-fill me-2"></i>
                <?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="bi bi-house-fill"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients/index.php">
                            <i class="bi bi-people-fill"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="communications/index.php">
                            <i class="bi bi-envelope-fill"></i> التواصل
                        </a>
                    </li>
                    <?php if ($user_role == 'admin' || $user_role == 'supervisor'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="reports/index.php">
                            <i class="bi bi-graph-up"></i> التقارير
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php if ($user_role == 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="admin/index.php">
                            <i class="bi bi-gear-fill"></i> الإدارة
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($user_name); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile/index.php">
                                <i class="bi bi-person"></i> الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="notifications/index.php">
                                <i class="bi bi-bell"></i> الإشعارات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php">
                                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Welcome Section -->
            <div class="col-12 mb-4">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <h2 class="card-title">مرحباً، <?php echo htmlspecialchars($user_name); ?></h2>
                        <p class="card-text">
                            <?php echo get_user_role_name($user_role); ?> - 
                            اليوم <?php echo date('Y-m-d'); ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-right-primary">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي العملاء
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($total_clients); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-people-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-right-success">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    رسائل اليوم
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($today_communications); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-envelope-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-right-warning">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    متابعات مطلوبة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($pending_followups); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-clock-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if ($user_role == 'admin' || $user_role == 'supervisor'): ?>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-right-info">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    الموظفون النشطون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($active_employees); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-person-badge-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning-fill"></i> إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="clients/add.php" class="btn btn-primary btn-block">
                                    <i class="bi bi-person-plus-fill"></i> إضافة عميل جديد
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="communications/send_email.php" class="btn btn-success btn-block">
                                    <i class="bi bi-envelope-plus-fill"></i> إرسال بريد إلكتروني
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="communications/send_whatsapp.php" class="btn btn-info btn-block">
                                    <i class="bi bi-whatsapp"></i> إرسال واتساب
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="reports/communications.php" class="btn btn-warning btn-block">
                                    <i class="bi bi-graph-up"></i> عرض التقارير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>
