-- Smart Communication System Database
-- Created for Jordan-based companies, schools, government institutions, and private sector

CREATE DATABASE IF NOT EXISTS smart_communication_system 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE smart_communication_system;

-- Users table (Employees)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'employee', 'supervisor') DEFAULT 'employee',
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Clients table
CREATE TABLE clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    organization VARCHAR(150) NOT NULL,
    sector ENUM('educational', 'government', 'private') NOT NULL,
    email VARCHAR(100),
    whatsapp_number VARCHAR(20),
    phone VARCHAR(20),
    address TEXT,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'potential') DEFAULT 'active',
    assigned_employee_id INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_employee_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_sector (sector),
    INDEX idx_organization (organization),
    INDEX idx_assigned_employee (assigned_employee_id)
);

-- Communication log table
CREATE TABLE communications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    employee_id INT NOT NULL,
    communication_type ENUM('email', 'whatsapp', 'phone', 'meeting') NOT NULL,
    subject VARCHAR(200),
    message TEXT,
    status ENUM('sent', 'delivered', 'read', 'replied', 'failed') DEFAULT 'sent',
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    response_received BOOLEAN DEFAULT FALSE,
    response_message TEXT,
    response_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_client_id (client_id),
    INDEX idx_employee_id (employee_id),
    INDEX idx_communication_type (communication_type),
    INDEX idx_sent_at (sent_at)
);

-- Email templates table
CREATE TABLE email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    body TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- WhatsApp templates table
CREATE TABLE whatsapp_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Notifications table
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('reminder', 'alert', 'info', 'warning') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read)
);

-- Login sessions table
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token)
);

-- System settings table
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default admin user (password: admin123 - should be changed)
INSERT INTO users (username, email, password, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin');

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('smtp_host', 'smtp.gmail.com', 'SMTP server host'),
('smtp_port', '587', 'SMTP server port'),
('smtp_username', '', 'SMTP username'),
('smtp_password', '', 'SMTP password'),
('whatsapp_api_url', '', 'WhatsApp API URL'),
('whatsapp_api_token', '', 'WhatsApp API token'),
('company_name', 'شركة الاتصالات الذكية', 'Company name'),
('company_email', '<EMAIL>', 'Company email'),
('reminder_days', '7', 'Days before sending reminder'),
('max_login_attempts', '5', 'Maximum login attempts before lockout');

-- Insert sample email templates
INSERT INTO email_templates (name, subject, body, created_by) VALUES
('welcome', 'مرحباً بكم في خدماتنا', 'عزيزي العميل،\n\nنرحب بكم في خدماتنا ونتطلع للعمل معكم.\n\nمع أطيب التحيات،\nفريق العمل', 1),
('follow_up', 'متابعة خدماتنا', 'عزيزي العميل،\n\nنود متابعة احتياجاتكم والتأكد من رضاكم عن خدماتنا.\n\nمع أطيب التحيات،\nفريق العمل', 1);

-- Insert sample WhatsApp templates
INSERT INTO whatsapp_templates (name, message, created_by) VALUES
('welcome', 'مرحباً بكم! نحن سعداء لخدمتكم ونتطلع للعمل معكم.', 1),
('reminder', 'تذكير: نود التواصل معكم لمتابعة احتياجاتكم. شكراً لكم.', 1);
